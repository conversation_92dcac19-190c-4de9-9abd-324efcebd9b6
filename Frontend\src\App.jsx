import React from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import HomePage from './components/pages/Home/HomePage';

// Import pages from pages directory
import About from './components/pages/About/AboutPage';
import Cart from './components/pages/Cart/CartPage';
import Checkout from './components/pages/Checkout/CheckoutPage';
import Contact from './components/pages/Contact/ContactPage';
import Login from './components/pages/Auth/LoginPage';
// import OrderConfirmation from './components/pages/OrderConfirmation/OrderConfirmationPage';
// import OrderDetails from './components/pages/OrderDetails/OrderDetailsPage';
// import Orders from './components/pages/Orders/OrdersPage';
// import ProductDetails from './components/pages/Product/ProductDetailPage';
// import Products from './components/pages/Products/ProductsPage';
// import Profile from './components/pages/Profile/ProfilePage';
import Register from './components/pages/Auth/SignupPage';
// import ReelDetails from './components/pages/Reels/ReelDetailPage';
import Reels from './components/pages/Reels/ReelsPage';
// import Wishlist from './components/pages/Wishlist/WishlistPage';

// Import protected route component
import ProtectedRoute from './components/auth/ProtectedRoute';
// import NotFoundPage from './components/pages/NotFound/NotFoundPage';

function App() {
  return (
    <Router>
      <Toaster position="top-right" />
      <Routes>
        {/* Set HomePage from components directory as the default route */}
        <Route path="/" element={<HomePage />} />
        
        {/* Auth routes */}
        <Route path="/login" element={<Login />} />
        <Route path="/register" element={<Register />} />
        
        {/* Public routes */}
        <Route path="/about" element={<About />} />
        <Route path="/contact" element={<Contact />} />
        <Route path="/reels" element={<Reels />} />
        <Route path="/reels/:id" element={<ReelDetails />} />
        
        {/* Protected routes */}
        <Route path="/cart" element={
          <ProtectedRoute>
            <Cart />
          </ProtectedRoute>
        } />
        <Route path="/checkout" element={
          <ProtectedRoute>
            <Checkout />
          </ProtectedRoute>
        } />
     
    
     
        
        
      </Routes>
    </Router>
  );
}

export default App;


